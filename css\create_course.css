select {
	-webkit-appearance: none;
	-moz-appearance: none;
	appearance: none;
	width: 100%;
	cursor: pointer;
}

select:disabled {
	background-color: #f2f2f2;
	cursor: not-allowed;
}

select::ms-expand {
	display: none;
}
.select-wrapper {
	position: relative;
	display: flex;
	align-items: center;
	width: 100%;
}

.select-wrapper ion-icon {
	position: absolute;
	top: 50%;
	right: 8px;
	transform: translateY(-50%);
	color: var(--dark);
	user-select: none;
	pointer-events: none;
}

.course-container {
	width: 100%;
}
.create-new-course {
	width: 100%;
	padding: 4px 12px;
	padding-right: 4px;
	border-radius: 36px;
	border: 1px solid var(--border-to);
	margin-bottom: 12px;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.create-new-course h4 {
	font-size: small;
	color: var(--dark);
}
.create-new-course .diff-container {
	font-size: small;
	display: flex;
	align-items: center;
	background: var(--light);
	font-style: italic;
	font-size: 12px;
	padding: 6px 12px;
	border-radius: 36px;
}
.diff-container span {
	display: block;
}
.diff-container span:first-child {
	color: var(--dark-grey);
}
.diff-container span:last-child {
	margin-left: 4px;
	color: var(--dark-grey);
}

.course-container h3 {
	font-size: small;
	color: var(--dark);
	font-size: small;
}
.create-section-h3 {
   margin-bottom: 6px;
}

.course-container form {
	display: flex;
	height: 100%;
	width: 100%;
	position: relative;
}
.course-left {
	width: 380px;
	padding: 24px;
	margin-right: 12px;
	border-radius: 12px;
	background: var(--light-gradient);
	min-height: calc(90vh - 49px);
}

.course-right {
	width: calc(100% - 380px);
	padding: 24px;
	padding-bottom: 10vh;
	border-radius: 12px;
	background: var(--light-gradient);
	min-height: calc(90vh - 49px);
	position: relative;
	overflow-y: scroll;
}
.course-right::-webkit-scrollbar {
	border-radius: 12px;
	background: transparent;
}
.course-right::-webkit-scrollbar-track {
	background: transparent;
}
.course-right::-webkit-scrollbar-thumb {
	background: transparent;
}

.course-right p {
	margin-bottom: 24px;
	width: 100%;
	color: var(--dark-grey);
}

.image-preview {
	width: 100%;
	margin-bottom: 6px;
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	position: relative;
}
.image-preview #removeImageButton {
	position: absolute;
	top: 12px;
	right: 12px;
	font-size: 12px;
	border: none;
	padding: 4px 12px;
	padding-left: 6px;
	padding-top: 2px;
	border-radius: 16px;
	color: #fff;
	background: #0009;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
}
#removeImageButton ion-icon {
	transform: translateY(2px);
	font-size: small;
}
.remove-btn-main {
	margin-left: 12px;
	margin-top: 2px;
	font-size: 12px;
	color: #fff;
	border: none;
	background: #0009;
	padding: 4px 12px;
	padding-left: 6px;
	border-radius: 36px;
	position: absolute;
	top: 10px;
	right: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.remove-btn-main ion-icon {
	font-size: small;
	color: #fff;
	margin-right: 6px;
}

.image-preview img,
.image-preview video,
.image-preview iframe,
.image-preview audio {
	width: 100%;
	object-fit: cover;
	border-radius: 12px;
}

#image-preview,
#certificate-preview {
	display: none;
	width: 100%;
	aspect-ratio: 5/4;
	object-fit: cover;
	margin-bottom: 12px;
	border: 2px dotted var(--grey);
}
.course-image-instruction {
	font-size: 13px;
	color: var(--dark);
	font-weight: light;
}
.form-item {
	width: 100%;
}
.form-item:not(:last-child) {
	margin-bottom: 12px;
}
.form-item h4 {
	color: var(--dark);
	font-size: small;
	margin-bottom: 12px;
	margin-top: 24px;
	margin-left: 12px;
}

.form-item input,
.form-item textarea,
.form-item select {
	width: 100%;
	padding: 8px 12px;
	background: var(--list);
	border: 1px solid var(--border-to);
	color: var(--dark-grey);
	font-size: 13px;
	font-weight: light;
	outline: none;
	transition: 0.1s ease-in;
}
.form-item input,
.form-item select {
	border-radius: 36px !important;
}
.form-item label {
	font-size: small;
}

.form-item textarea {
	position: relative;
	resize: vertical;
	min-height: 320px;
	display: block;
	background: var(--light);
}

/*-----------section------------*/
.section {
	position: relative;
	padding: 12px;
	border-radius: 16px;
	transition: all 0.2s ease-in-out;
	background: var(--ash);
}
.section:not(:nth-child(1)) {
	margin-top: 10vh;
}
.section input,
.section textarea {
	border: none;
	padding: 8px 12px;
	outline: none;
	border: 1px solid var(--border-to);
}
.section h4:not(.component-item h4) {
	color: var(--dark);
	font-size: small;
	margin-left: 6px;
}

.section-content {
	display: flex;
	align-items: center;
	width: 100%;
	overflow-x: hidden;
	border: none;
	justify-content: space-between;
	border-radius: 36px;
	margin-bottom: 12px;
}
.section-content input {
	width: 100%;
	border-radius: 36px !important;
	padding: 8px 12px !important;
	background: var(--light);
	border: 1px solid var(--dark-grey);
}
.dynamic-button {
	flex: 1;
}
.dynamic-button .btn-container {
	display: flex;
	align-items: center;
	width: 70%;
	border-radius: 36px;
	border: none;
	background: var(--ash);
	flex: 1;
	width: 100%;
	justify-content: space-between;
}
.btn-container button {
	white-space: nowrap;
	flex: 1;
	padding: 8px 12px;
	background: var(--dark);
	color: var(--light);
	font-weight: 400;
	font-size: small;
	transition: all 0.2s ease;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}
.btn-container button span {
	margin-left: 6px;
}
.btn-container button:hover {
	background: var(--blue);
	color: #fff;
}
.btn-container button:not(:last-child) {
	margin-right: 12px;
}
.add-save-container {
	margin-top: 12px;
	padding: 6px;
}
.section-top-controls .removeSection {
	border: none;
	background: var(--btn-blue);
	cursor: pointer;
	color: #ffffff;
	font-size: 12px;
	padding: 6px 10px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
	transition: background-color 0.2s ease;
}
.section-top-controls .removeSection:hover {
	background: #e63946;
}
.section-top-controls .confirmRemove,
.section-top-controls .cancelRemove {
	padding: 4px 10px;
	border: none;
	font-size: 12px;
	background: var(--dark);
	color: var(--light);
	border-radius: 4px;
	transition: background-color 0.2s ease;
}
.section-top-controls .confirmRemove {
	color: #fff;
	background: var(--red);
}
.section-top-controls .confirmRemove:hover {
	background: #e63946;
}
.section-top-controls .cancelRemove:hover {
	background: var(--dark-grey);
}

/* Keep original styles for any remaining removeSection buttons not in top controls */
.removeSection {
	border: none;
	background: var(--btn-blue);
	cursor: pointer;
	color: #ffffff;
	font-size: 12px;
	padding: 4px 12px !important;
	padding-bottom: 5px !important;
	display: flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
}
.removeSection:hover {
	background: #e63946;
}
.confirmRemove,
.cancelRemove {
	padding: 4px 12px;
	border: none;
	font-size: small;
	background: var(--dark);
	color: var(--light);
	padding-bottom: 5px;
}
.confirmRemove {
	color: #fff;
	background: var(--red);
}
.confirmRemove:hover {
	background: #e63946;
}

.add-section {
	padding: 6px 12px;
	border: 1px solid var(--btn-blue);
	font-size: 13px;
	letter-spacing: 0.5px;
	background: var(--btn-blue);
	color: #ffffff;
	cursor: pointer;
	margin-right: 12px;
}
.section-count-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 18px;
}
.section-count-container h4 {
	margin-right: 6px;
}

.section-difficulty-controls {
	display: flex;
	align-items: center;
	gap: 12px;
	flex: 1;
	justify-content: center;
}

.difficulty-label {
	font-size: small;
	color: var(--dark-grey);
	white-space: nowrap;
}

.add-section:hover {
	background: var(--dark);
	border-color: var(--dark);
	color: var(--light);
}
.save-course {
	background: var(--light);
	padding: 6px 12px;
	border: 1px solid var(--dark);
	font-size: 13px;
	letter-spacing: 0.5px;
	color: var(--dark);
	cursor: pointer;
	position: relative;
}
.preview-module {
	background: var(--light);
	padding: 6px 12px;
	border: 1px solid var(--dark);
	font-size: small;
	letter-spacing: 0.5px;
	color: var(--dark);
	cursor: pointer;
	position: relative;
}

.save-course .tooltip {
	position: absolute;
	bottom: calc(100% + 4px);
	left: 0;
	background: rgb(255, 232, 232);
	color: var(--btn-blue);
	padding: 2px 12px;
	border-radius: 36px;
	font-size: 12px;
	white-space: nowrap;
	display: none;
}
.save-course:hover .tooltip {
	display: block !important;
}

/*----section-components----*/
.component-item:not(.multiple_choice_set.component-item) {
	display: flex;
	justify-content: end;
	align-items: center;
}
.media-container,
.lesson-container,
.url-container,
.identification-container,
.choice-container,
.essay-container,
.podcast-container {
	position: relative;
	border: 2px dotted var(--border-to);
	padding: 12px;
	padding-bottom: 0 !important;
	background: var(--light-panel);
	border-radius: 12px;
	width: 100%;
	margin-bottom: 12px;
}
.media-container p,
.lesson-container p,
.url-container p,
.podcast-container,
.identification-container p,
.choice-container p,
.essay-container p {
	margin-bottom: 12px;
}

.component-item h4,
.component-item input,
.component-item textarea {
	width: 100%;
}
.component-item h4 {
	font-size: 13px;
	margin-bottom: 24px;
	color: var(--dark);
}
.component-item input,
.component-item textarea {
	background: var(--list);
	border: 1px solid var(--border-to);
}
.component-item textarea {
	resize: vertical;
	min-height: 100px;
	display: block;
	margin-bottom: 12px;
}
.component-item input {
	margin-bottom: 12px;
}
.component-item input[type="file"] {
	font-size: 12px;
	padding-top: 6px !important;
	padding-bottom: 6px !important;
	padding-left: 6px !important;
	margin-bottom: 0;
}
.narrative-attachment {
	margin-bottom: 12px !important;
}
.component-item input {
	border-radius: 36px !important;
}

/* Component Controls Container */
.component-controls {
	position: absolute;
	top: 12px;
	right: 12px;
	display: flex;
	gap: 12px;
	align-items: center;
}

.swapUp, .swapDown {
	border: none;
	cursor: pointer;
	font-weight: 300;
	font-size: 11px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #fff8;
	color: var(--dark-grey);
	border-radius: 4px;
	transition: all 0.2s ease;
	min-width: 28px;
	height: 28px;
}

.swapUp:hover, .swapDown:hover {
	background: var(--btn-blue);
	color: white;
}

.swapUp ion-icon, .swapDown ion-icon {
	font-size: 14px;
}

.removeComponent {
	border: 1px solid var(--btn-blue);
	cursor: pointer;
	font-weight: 400;
	font-size: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	background: none;
	padding: 4px 12px;
	color: var(--btn-blue);
	border-radius: 4px;
	transition: all 0.2s ease;
}

.removeComponent:hover {
	background: var(--btn-blue);
	color: white;
}

/* Validation Warning Panel */
.validation-warning-panel {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10000;
}

.warning-content {
	background: white;
	border-radius: 12px;
	padding: 24px;
	max-width: 500px;
	width: 90%;
	max-height: 80vh;
	overflow-y: auto;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.warning-header {
	display: flex;
	align-items: center;
	gap: 12px;
	margin-bottom: 16px;
	padding-bottom: 12px;
	border-bottom: 1px solid var(--border-to);
}

.warning-header ion-icon {
	font-size: 24px;
	color: #ff6b35;
}

.warning-header h3 {
	margin: 0;
	color: var(--dark);
	font-size: 18px;
}

.warning-body {
	margin-bottom: 20px;
}

.warning-body p {
	margin-bottom: 12px;
	color: var(--dark-grey);
	line-height: 1.5;
}

.error-list {
	background: var(--light);
	border-radius: 8px;
	padding: 12px;
	margin: 12px 0;
	max-height: 200px;
	overflow-y: auto;
}

.error-list p {
	margin: 4px 0;
	font-size: 14px;
	color: var(--dark);
}

.warning-footer {
	display: flex;
	justify-content: flex-end;
}

.close-warning-btn {
	background: var(--btn-blue);
	color: white;
	border: none;
	padding: 8px 20px;
	border-radius: 6px;
	cursor: pointer;
	font-size: 14px;
	transition: background 0.2s ease;
}

.close-warning-btn:hover {
	background: var(--dark);
}

/* Disabled Preview Button */
.preview-module.disabled {
	background: var(--dark-grey) !important;
	color: #999 !important;
	cursor: not-allowed !important;
	opacity: 0.6;
}

.preview-module.disabled:hover {
	background: var(--dark-grey) !important;
	color: #999 !important;
}

.new-choice-container {
	display: flex;
	margin-bottom: 12px;
}
.addChoice {
	margin-bottom: 24px;
	margin-top: 12px;
	border: none;
	background: var(--blue);
	color: #f4f4f4;
	padding: 1px 8px;
	padding-bottom: 2px;
}
.removeChoice {
	border: none;
	background: none;
	color: var(--btn-blue);
	margin-left: 24px;
	font-size: medium;
	white-space: nowrap;
}

.removeChoice-icon {
	background: none;
	padding: 0;
}
.removeChoice-icon ion-icon {
	color: var(--btn-blue);
	font-size: medium;
   transform: translateY(2px);
}
.new-choice-container input {
	margin-bottom: 0;
}

/*-----Input type file---*/

.media-container .upload-box {
	font-size: 13px;
	width: 100%;
	outline: none;
	cursor: pointer;
	color: var(--dark-grey);
	font-weight: 300;
	padding: 6px;
}

#fileInputImage {
	font-size: 12px;
	padding-top: 6px;
	padding-bottom: 6px;
	padding-left: 6px;
}

::-webkit-file-upload-button {
	color: #f4f4f4;
	background: var(--btn-blue);
	border: none;
	border-radius: 50px;
	margin: 0;
	margin-right: 24px;
	cursor: pointer;
	padding: 6px 24px;
	font-size: 11px !important;
}
#myfile {
	margin-top: 0;
	border-radius: 36px;
	background: var(--grey);
	cursor: pointer;
	padding: 6px;
}
#certificate {
	margin-top: 0;
	border-radius: 36px;
	background: var(--grey);
	cursor: pointer;
	padding: 12px;
}
/* Initial hidden state for all grouped buttons */
.learning-content,
.assessment {
	display: none;
	margin-top: 12px; /* Optional for spacing */
	border-radius: 36px;
	background: var(--list);
   padding: 6px 0;
}
.learning-content button,
.assessment button {
	padding: 6px 12px;
	background: var(--light);
	color: var(--dark);
	min-width: 100px;
	margin-right: 6px;
	transition: all 0.2s ease;
}
.learning-content button:hover,
.assessment button:hover {
	background: var(--blue);
	color: #fff;
}

.choice-item {
	display: flex;
	align-items: center;
	width: 100%;
	background: var(--list);
	border-radius: 36px;
	border: 1px solid var(--border-to);
}
.choice-item:not(:last-child) {
	margin-bottom: 12px;
}
.choice-item input {
	margin-bottom: 0;
}
.choice-item input[type="text"] {
	width: 100% !important;
	border: none;
}
.choice-item input[type="radio"] {
	width: auto;
	margin-left: 12px;
	cursor: pointer;
}
.addChoice {
	margin: 0;
	margin-bottom: 12px;
	margin-top: 12px;
	padding: 4px 12px;
	font-size: small;
}
.removeChoice {
	margin-right: 6px;
}
.addChoice-button-container {
	display: flex;
	align-items: center;
	justify-content: end;
	position: absolute;
	top: 12px;
	right: 12px;
}
.addChoice-button-container button {
	border: none;
	background: none;
}
.addChoice-button-container button:first-child {
	font-size: 12px;
	display: flex;
	align-items: center;
	color: green;
}
.addChoice-button-container button:first-child img {
	margin-right: 6px;
	width: 8px;
	filter: invert(41%) sepia(91%) saturate(466%) hue-rotate(71deg)
		brightness(96%) contrast(92%);
}
.addQuestion-remove-btn {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 6px;
}
.addQuestion-remove-btn p {
	display: block;
	font-style: italic;
	font-weight: 300;
	margin-bottom: 0;
	flex: initial;
	margin-left: 12px;
	width: max-content;
	color: green;
}
.addQuestion-remove-btn p span {
	font-size: small;
	color: green;
	font-weight: 400;
	margin-left: 6px;
}
.addChoice-button-container button:last-child {
	background: none;
	border: 1px solid var(--btn-blue);
	color: var(--btn-blue);
	margin-left: 12px;
	padding: 2px 6px;
	font-size: 12px;
}
.removeQuestion {
   border: none !important;
}

.section.collapsed .section-component,
.section.collapsed .dynamic-button,
.section.collapsed .dynamic-panel,
.section.collapsed .learning-content,
.section.collapsed .assessment,
.section.collapsed .component-item {
	opacity: 0; /* Make elements invisible */
	height: 0; /* Collapse height */
	overflow: hidden; /* Prevent overflow */
	pointer-events: none; /* Disable interactions */
	transition: opacity 0.2s ease, height 0.2s ease; /* Smooth transitions for better UX */
}

.section-content,
.section-component,
.dynamic-button,
.dynamic-panel,
.learning-content,
.assessment,
.component-item {
	transition: opacity 0.2s ease, height 0.2s ease; /* Add transitions for smooth effect */
}

.section-top-controls {
	position: absolute;
	top: 12px;
	right: 12px;
	display: flex;
	align-items: center;
	gap: 8px;
}

.toggle-button {
	border: none;
	padding: 5px 10px;
	background: var(--light);
	color: var(--dark-grey);
	font-size: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	border-radius: 4px;
	transition: background-color 0.2s ease;
	gap: 6px;
}

.toggle-button img {
	flex-shrink: 0;
   opacity: 0.5;
}

.toggle-text {
	font-size: 12px;
	font-weight: 400;
	white-space: nowrap;
	color: var(--dark-grey) !important;
}

.toggle-button:hover {
	background: var(--grey);
}

/* Dark mode styles for toggle button icons and text */
.dark .toggle-button img {
	filter: brightness(0) invert(1);
}

.dark .toggle-text {
	color: var(--light);
}

.dark .toggle-button:hover {
	background: var(--border-to);
}

.reading-preview {
	width: calc(380px - 48px);
	position: relative;
}
.file-content {
	width: 100%;
	display: flex;
	margin-bottom: 12px;
}
.component-content {
	width: 100%;
}

.reading-preview img,
.reading-preview video,
.reading-preview audio,
.reading-preview iframe {
	width: 100%;
	object-fit: cover;
	border-radius: 12px;
	margin: 2px;
}

.file-information-container button {
	position: absolute;
	top: 12px;
	right: 12px;
	font-size: 12px;
	border: none;
	padding: 4px 12px;
	padding-left: 6px;
	border-radius: 16px;
	color: #fff;
	background: #0009;
	cursor: pointer;
	display: flex;
	align-items: center;
}
.file-information-container button ion-icon {
	margin-right: 6px;
	font-size: small;
	transform: translateY(-1px);
}
.file-information-container p:not(.info-title) {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow-x: scroll;
	width: calc(100% - 24px);
}
.file-information-container p:not(.info-title)::-webkit-scrollbar {
	height: 0;
	width: 0;
}
.remove-preview {
	border: 1px solid var(--dark-grey);
}
/* Styling for the loading panel */
.loading-panel {
	border: 1px solid var(--border-to);
	width: 100%;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	border-radius: 12px;
	display: none;
}

/* Horizontal loading bar container */
.loading-bar {
	width: 100%; /* Adjust width as needed */
	height: 4px;
	background-color: var(--light); /* Light gray background */
	border-radius: 12px; /* Rounded edges */
	overflow: hidden; /* Clip overflowing content */
	position: relative; /* Position for progress animation */
}

/* Animated progress */
.progress {
	width: 0%; /* Initial width */
	height: 100%;
	background-color: #3498db; /* Blue color for the progress bar */
	animation: load 2s infinite; /* Looping animation */
	border-radius: 12px; /* Rounded edges for smooth appearance */
}

/* Keyframes for progress animation */
@keyframes load {
	0% {
		width: 0%;
	}
	50% {
		width: 70%;
	}
	100% {
		width: 100%;
	}
}

#difficultyContainer {
	position: absolute;
	top: 12px;
	right: 6px;
	width: max-content;
	display: flex;
	align-items: center;
	opacity: 0;
}
#difficultyContainer span {
	color: var(--dark);
	font-size: small;
	background: var(--grey);
	border-radius: 16px;
	padding: 4px 12px;
}
#difficultyContainer i {
	font-size: 12px;
	color: var(--dark-grey);
	font-style: normal;
	margin-right: 12px;
}

/* Remove default appearance */
input[type="range"] {
	-webkit-appearance: none;
	appearance: none;
	max-width: 380px;
	margin: 0;
	margin-right: 12px;
	padding: 0;
	border: none;
	background: var(--dark);
	border-radius: 36px;
}

/* Track style */
input[type="range"]::-webkit-slider-runnable-track {
	width: 100%;
	height: 2px;
	background: var(--dark);
	border-radius: 8px;
	border: none;
}

/* Firefox track style */
input[type="range"]::-moz-range-track {
	width: 100%;
	height: 2px;
	background: var(--dark);
	border-radius: 8px;
	border: none;
}

/* Thumb/handle style */
input[type="range"]::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	width: 14px;
	height: 14px;
	background-color: var(--gold);
	border-radius: 50%;
	cursor: pointer;
	transition: background-color 0.2s ease;
	margin-top: -6px;
	border: 2px solid var(--light);
}

/* Firefox thumb style */
input[type="range"]::-moz-range-thumb {
	width: 14px;
	height: 14px;
	background-color: var(--gold);
	border-radius: 50%;
	cursor: pointer;
	border: 2px solid var(--light);
}

.section-difficulty-input {
	border: 1px solid var(--dark-grey) !important;
	width: 60px !important;
	padding: 2px !important;
   padding-left: 8px !important;
	border-radius: 36px !important;
}

.section-slider {
	width: 120px;
	height: 2px;
	border-radius: 3px;
	background: var(--light);
	outline: none;
	-webkit-appearance: none;
   appearance: none;
}

.section-slider::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	width: 18px;
	height: 18px;
	border-radius: 50%;
	background: var(--btn-blue);
	cursor: pointer;
	border: 2px solid white;
	box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.section-slider::-moz-range-thumb {
	width: 18px;
	height: 18px;
	border-radius: 50%;
	background: var(--btn-blue);
	cursor: pointer;
	border: 2px solid white;
	box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}
/* For WebKit browsers (e.g., Chrome, Safari) */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
	-webkit-appearance: auto; /* Ensures the arrows are visible */
	appearance: auto;
	margin: 0;
	background: var(--light);
}

/* For Firefox */
input[type="number"] {
	-moz-appearance: textfield; /* Disable default styles */
	appearance: auto;
	appearance: auto; /* Ensures spin buttons are visible */
	background: var(--light);
}
.section-hashtag {
	margin-left: 6px;
   font-weight: 400;
}

/* the other DIV stylesheet location: admin_header.css */

.publish-content .publish-item {
	margin-bottom: 12px;
}
.publish-content h3 {
	font-size: medium;
	margin-bottom: 12px;
}
.publish-content p {
	margin-bottom: 12px;
}

.preview-container {
	display: flex;
	flex-direction: row-reverse;
}

.left-section,
.right-section {
	border: 1px solid var(--border-to);
	border-radius: 12px;
	background: var(--light);
	overflow-y: scroll;
	height: calc(70vh - 42px - 24px);
	position: relative;
}
.right-section::-webkit-scrollbar,
.left-section::-webkit-scrollbar {
	width: 0;
	height: 0;
}
.right-section {
	margin-right: 12px;
	flex: 1;
	padding: 24px;
}
.left-section {
	width: 380px;
	padding: 12px;
}
.left-section h3:not(.section-preview h3) {
	font-size: small;
	margin-left: 6px;
}
.section-preview h3 {
	background-color: var(--list);
	padding: 8px 12px;
	border: 1px solid var(--border-to);
	font-size: small;
	border-radius: 36px;
	margin-bottom: 5px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.component-preview h4 {
	border: 1px solid var(--border-to);
	padding: 8px 12px;
	border-radius: 36px;
	margin-bottom: 5px;
	font-size: small;
	font-weight: 400;
	padding-left: 42px;
	position: relative;
	cursor: pointer;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	color: var(--dark-grey);
}
.component-preview h4 span {
	margin-left: 12px;
}
.component-preview h4::before {
	content: "";
	width: 13px;
	height: 13px;
	border-radius: 50%;
	border: 1px solid #ccc;
	position: absolute;
	left: 12px;
	top: 50%;
	transform: translateY(-50%);
}
.text-content {
	margin-bottom: 24px;
	font-size: small;
}
.text-content:first-child {
	font-size: large;
	font-weight: bold;
	margin-bottom: 6px;
}
.text-content:nth-child(2) {
	color: var(--dark-grey);
}
#detail_content iframe {
	border: 1px solid var(--border-to);
	width: 100%;
	aspect-ratio: 16/9;
	margin-bottom: 12px;
	border-radius: 12px;
}
#detail_content img {
	width: 100%;
	border-radius: 12px;
}
#detail_content video {
	width: 100%;
	border-radius: 12px;
}

.module-info-container {
	margin-top: 12px;
	display: flex;
	width: 100%;
}
.module-info-item {
	border: 1px solid var(--border-to);
	border-radius: 12px;
}
.module-info-item img {
	border-radius: 12px;
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.module-info-item:first-child {
	width: 380px;
	margin-right: 12px;
}
.module-info-item:nth-child(2) {
	width: calc(100% - 380px);
	padding: 24px;
   border: none;
   background: var(--ash);
   min-height: 24vh;
}
#modal_designation {
	width: max-content;
	color: var(--dark-grey);
	margin-bottom: 6px;
	margin-top: 24px;
}
#modal_category {
	width: max-content;
	margin-bottom: 6px;
   text-transform: capitalize;
}
#modal_plantilla {
   width: max-content;
   text-transform: capitalize;
}
#modal_course_description {
   margin-top: 24px;
}

.publish-buttons {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-top: 24px;
	padding-left: 6px;
}
.publish-buttons-item button {
	padding: 8px 12px;
	border-radius: 36px;
	background: var(--dark);
	color: var(--light);
	font-size: small;
	border: none;
	cursor: pointer;
	transition: all 0.2s ease;
	margin-right: 12px;
}
.publish-buttons-item #closeModal {
	background: var(--light);
	color: var(--dark);
	font-size: small;
	display: flex;
	align-items: center;
	border: none;
	padding: 0;
	border-radius: 0 !important;
}
.publish-buttons-item #closeModal ion-icon {
	color: var(--btn-blue);
}

#publishModal #closeModal span {
	display: block;
	padding: 6px 12px;
	background: var(--btn-blue);
	border-radius: 36px;
	border: none;
	color: #fff;
}

.publish-buttons-item #closeModal:hover {
	background: var(--light) !important;
	color: var(--dark);
}
.short-assessment {
	margin-bottom: 6px;
}
.short-assessment:first-child {
	margin-bottom: 24px;
}
.short-assessment:not(:first-child) {
	border: 1px solid var(--border-to);
	padding: 4px 12px;
	border-radius: 36px;
	position: relative;
	padding-left: 36px;
}
.short-assessment:not(:first-child)::before {
	content: "";
	width: 10px;
	height: 10px;
	border-radius: 50%;
	border: 1px solid var(--dark-grey);
	position: absolute;
	left: 8px;
	top: 50%;
	transform: translateY(-50%);
}

.reflective-writing:first-child {
	display: none;
}
.reflective-writing:nth-child(2) {
	font-weight: bold;
	font-size: large;
	position: relative;
}
.reflective-writing:nth-child(2):after {
	content: "Type your essay here";
	width: calc(100% - 24px - 1px);
	height: 40vh;
	display: block;
	border: 1px solid var(--border-to);
	margin-top: 24px;
	border-radius: 12px;
	font-size: small;
	font-weight: 400;
	padding-top: 24px;
	padding-left: 24px;
	color: #ccc;
}
.reflective-writing:nth-child(3) {
	margin-top: 24px;
	font-size: small;
	color: var(--dark-grey);
	padding: 2px 12px;
	border-radius: 36px;
	border: 1px solid var(--border-to);
	width: max-content;
}
.reflective-writing:nth-child(3):after {
	content: "Words Remainning";
	margin-left: 12px;
}
.multiple_choice_set {
	margin-bottom: 12px;
	padding: 12px;
	border-radius: 12px;
	background: var(--light);
	border: 1px dashed #ccc;
	position: relative;
}

.questions-container {
	overflow-x: auto;
	white-space: nowrap;
	width: 100%;
	display: flex;
	padding-bottom: 12px;
	margin-bottom: 12px;
	margin-top: 12px;
}
.questions-container::-webkit-scrollbar-thumb {
	background: #bbe0c4;
}

.multiple_choice {
	border: 1px solid var(--border-to);
	border-radius: 12px;
	margin-right: 12px;
	padding: 12px;
	flex: 0 0 calc(50% - 6px);
	box-sizing: border-box;
	border: 1px solid rgba(104, 104, 104, 0.1);
	position: relative;
	overflow: hidden;
	background: var(--light);
}
.multiple_choice h4 {
	margin-bottom: 12px;
}

.multiple_choice input:not(.choice input) {
	background: var(--light);
	width: 100%;
	border-radius: 36px !important;
	margin-bottom: 12px;
}
.choice {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	border: 1px solid var(--border-to);
	border-radius: 36px;
	padding-right: 6px;
}
.choice input {
	margin-bottom: 0;
	border: none;
}
.choice input[type="radio"] {
	width: auto;
	margin-left: 12px;
	cursor: pointer;
}
.choice:not(:last-child) {
	margin-bottom: 12px;
}

.addChoice_set img {
	width: 12px;
}

.mcs-container-title {
	display: flex;
	align-items: baseline;
}
.mcs-container-title input {
	width: 50%;
}
.mcs-container-title h4 {
	width: max-content;
	white-space: nowrap;
	margin-right: 24px;
}

.addQuestion-button-container {
	display: flex;
	align-items: center;
}
.addMoreQuestions,
.removeSet {
	padding: 4px 12px;
	border-radius: 36px;
	font-size: 12px;
	border: none;
	color: var(--dark);
	background: var(--gold);
	white-space: nowrap;
}
.addMoreQuestions {
   padding: 6px 12px;
   color: #fff;
}
.removeSet {
	background: none;
	border: 1px solid var(--btn-blue);
	color: var(--btn-blue);
}

.addShortReading,
.edit_addShortReading {
	background: #fff3cd !important;
	color: #856404 !important;
   border: 1px solid #85650456;
}
.addShortVideo,
.edit_addShortVideo {
	background: #f8d7da !important;
	color: #721c24 !important;
   border: 1px solid #721c2556;
}
.addLectureCast,
.edit_addLectureCast {
	background: #d1ecf1 !important;
	color: #0c5460 !important;
   border: 1px solid #0c536056;
}
.addPodcast,
.edit_addPodcast {
	background: #eaf7e1 !important;
	color: #2e7d32 !important;
   border: 1px solid #2e7d3256;
}

.addReflectiveWriting,
.edit_addReflectiveWriting {
	background: #cce5ff !important;
	color: #004085 !important;
   border: 1px solid #0040855b;
}
.addMultipleChoiceSet,
.edit_addMultipleChoiceSet {
	background: #d4edda !important;
	color: #155724 !important;
   border: 1px solid #15572457;
}

.short-reading-color {
	background: #fff3cdc4 !important;
	color: #856404 !important;
}
.short-reading-color input,
.short-reading-color textarea,
.short-reading-color h4,
.short-reading-color input::placeholder,
.short-reading-color textarea::placeholder {
	color: #856404 !important;
	background: var(--light);
}
.short-reading-color input,
.short-reading-color textarea {
   border: 1px solid #85640456;
}
.short-reading-color h4 {
	background: none;
}

.short-video-color {
	background: #f8d7dab2 !important;
	color: #721c24 !important;
}
.short-video-color input,
.short-video-color textarea,
.short-video-color h4,
.short-video-color input::placeholder,
.short-video-color textarea::placeholder {
	color: #721c24 !important;
	background: var(--light);
}
.short-video-color input,
.short-video-color textarea {
   border: 1px solid #721c2456;
}

.short-video-color h4 {
	background: none;
}

.lecture-cast-color {
	background: #d1ecf1c0 !important;
	color: #0c5460 !important;
}
.lecture-cast-color input,
.lecture-cast-color textarea,
.lecture-cast-color h4,
.lecture-cast-color input::placeholder,
.lecture-cast-color textarea::placeholder {
	color: #0c5460 !important;
	background: var(--light);
}
.lecture-cast-color input,
.lecture-cast-color textarea {
   border: 1px solid #0c546056;
}
.lecture-cast-color h4 {
	background: none;
}

.podcast-color {
	background: #eaf7e1c0 !important;
	color: #2e7d32 !important;
}
.podcast-color input,
.podcast-color textarea,
.podcast-color h4,
.podcast-color input::placeholder,
.podcast-color textarea::placeholder {
	color: #2e7d32 !important;
	background: var(--light);
}
.podcast-color input,
.podcast-color textarea {
   border: 1px solid #2e7d3256;
}
.podcast-color h4 {
	background: none;
}

.reflective-writing-color {
	background: #cce5ffc0 !important;
	color: #004085 !important;
}
.reflective-writing-color input,
.reflective-writing-color textarea,
.reflective-writing-color h4,
.reflective-writing-color input::placeholder,
.reflective-writing-color textarea::placeholder {
	color: #004085 !important;
	background: var(--light);
}
.reflective-writing-color input,
.reflective-writing-color textarea {
   border: 1px solid #00408556;
}
.reflective-writing-color h4 {
	background: none;
}

.multiple_choice_set-color {
	background: #d4eddabd !important;
	color: #155724 !important;
}
.multiple_choice_set-color input,
.multiple_choice_set-color textarea,
.multiple_choice_set-color h4,
.multiple_choice_set-color input::placeholder,
.multiple_choice_set-color textarea::placeholder {
	color: #155724 !important;
	background: var(--light);
}
.choice input[type="text"] {
   border: none;
}
.multiple_choice_set-color input,
.multiple_choice_set-color textarea {
   border: 1px solid #15572456;
}
.multiple_choice_set-color h4 {
	background: none;
	margin-left: 0 !important;
	margin-bottom: 24px;
}
.multiple_choice_set-color h4:not(.mcs-container-title h4) {
	width: max-content;
}

#detail_content {
	width: 100%;
}
.question-title {
	font-size: large;
	font-weight: bold;
	margin-bottom: 24px !important;
}
.user-answer {
	background: var(--light);
	border: 1px solid orange;
	padding: 8px 12px;
	display: flex;
	align-items: center;
	margin-bottom: 6px;
	width: 100%;
	border: 1px solid var(--border-to);
	border-radius: 36px;
}

.question-title:not(:first-child) {
	margin-top: 24px;
}

.user-answer input {
	margin-right: 12px;
}
.user-answer label {
	display: block;
	font-size: small;
}
.note {
	margin-top: 24px;
	font-size: small;
	margin-bottom: 0 !important;
}
.submit-answer-mc {
	background: var(--dark);
	padding: 4px 12px;
	color: var(--light);
	margin-top: 24px;
	cursor: not-allowed;
}

/* URL Validation Styles */
.url-input-container {
	position: relative;
	display: inline-block;
	width: 100%;
}

.url-validation-tip {
	position: absolute;
	top: -35px;
	left: 50%;
	transform: translateX(-50%);
	padding: 5px 10px;
	border-radius: 4px;
	font-size: small;
	white-space: nowrap;
	z-index: 1000;
	opacity: 0;
	visibility: hidden;
	transition: opacity 0.3s ease, visibility 0.3s ease;
	pointer-events: none;
}

.url-validation-tip.valid {
	background-color: #4caf50;
	color: white;
}

.url-validation-tip.invalid {
	background-color: #f44336;
	color: white;
}

.url-validation-tip.show {
	opacity: 1;
	visibility: visible;
}

.url-validation-tip::after {
	content: "";
	position: absolute;
	top: 100%;
	left: 50%;
	transform: translateX(-50%);
	border: 5px solid transparent;
}

.url-validation-tip.valid::after {
	border-top-color: #4caf50;
}

.url-validation-tip.invalid::after {
	border-top-color: #f44336;
}

/* URL Input Color States */
input[type="url"].valid-url {
	color: #4caf50 !important;
	border-color: #4caf50 !important;
}

input[type="url"].invalid-url {
	color: #f44336 !important;
	border-color: #f44336 !important;
}

@media screen and (max-width: 1080px) {
	.course-container form {
		display: block;
	}
	.course-left {
		width: 100%;
		margin-right: 0;
	}
	.course-right {
		width: 100%;
		margin-top: 4px;
	}
	.course-right p {
		width: 100%;
	}

	.section input,
	.section textarea {
		padding: 6px 8px;
	}
	.section-content {
		display: block;
		border-radius: 16px;
		border: 1px solid var(--grey);
		padding: 6px;
	}
	.section-content input {
		width: 100%;
		margin-right: 0;
	}

	.dynamic-button {
		margin-top: 24px;
		margin-left: 0;
		margin-right: 0;
	}
	.dynamic-button .btn-container {
		display: block;
		margin-left: 0;
		width: 100%;
		border-radius: 12px;
		padding: 12px;
		padding-bottom: 0;
	}
	.dynamic-button .btn-container button {
		margin-bottom: 12px;
		padding: 8px 12px;
		width: 100%;
	}
	.dynamic-button .btn-container button p {
		visibility: visible;
		width: 100%;
		margin-left: 12px;
	}

	.add-section {
		margin-bottom: 12px;
		width: 100%;
		margin-right: 0;
		padding: 12px;
	}
	.save-course {
		width: 100%;
		padding: 12px;
	}
	.removeSection {
		font-size: 13px;
		position: relative;
		margin-top: 24px;
		width: 100%;
		padding: 8px;
	}
	.url-container,
	.media-container,
	.lesson-container,
	.identification-container,
	.choice-container,
	.essay-container,
	.podcast-container {
		width: 100%;
	}
	.add-save-container {
		border-radius: 12px;
	}
	.learning-content,
	.assessment {
		border-radius: 16px;
	}
	.learning-content button,
	.assessment button {
		margin-right: 0;
		width: 100%;
	}
	.learning-content button:not(:last-child),
	.assessment button:not(:last-child) {
		margin-bottom: 8px;
	}
	.section-count-container {
		display: block;
	}
	.section-difficulty-controls {
		flex-direction: column;
		align-items: flex-start;
		gap: 8px;
		margin-top: 12px;
	}
	.section-count-container input {
		width: 100% !important;
		margin: 0;
		padding: 8px !important;
	}
	.section-slider {
		width: 100% !important;
	}
	.section-count-container button {
		width: 100%;
		margin: 0;
		margin-top: 12px;
		padding: 8px;
	}
	input[type="range"] {
		padding: 0 !important;
		margin-top: 24px;
	}
	.confirmRemove,
	.cancelRemove {
		width: calc(50% - 6px);
		margin-top: 24px;
	}
	.confirmRemove {
		margin-right: 6px;
	}
}

/* Leave Confirmation Panel Styles */
.leave-confirmation-overlay {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	background: rgba(0, 0, 0, 0.5);
	backdrop-filter: blur(4px);
	z-index: 10000;
	align-items: center;
	justify-content: center;
	user-select: none;
	-webkit-user-select: none;
	-ms-user-select: none;
}

.leave-confirmation-content {
	background: var(--ash);
	border-radius: 12px;
	padding: 24px;
	max-width: 400px;
	width: 90%;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
	border: 1px solid var(--border-to);
	animation: slideIn 0.3s ease-out;
}

.leave-confirmation-header h3 {
	margin: 0 0 16px 0;
	color: var(--dark);
	font-size: 18px;
	font-weight: 600;
}

.leave-confirmation-body p {
	margin: 0 0 24px 0;
	color: var(--dark);
	font-size: 14px;
	line-height: 1.5;
}

.leave-confirmation-buttons {
	display: flex;
	gap: 12px;
	justify-content: flex-end;
}

.leave-btn {
	padding: 8px 16px;
	border: none;
	border-radius: 6px;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.2s ease;
	min-width: 80px;
}

.leave-btn-primary {
	background: #dc3545;
	color: white;
}

.leave-btn-primary:hover {
	background: #c82333;
}

.leave-btn-secondary {
	background: var(--light);
	color: var(--dark);
	border: 1px solid var(--border-to);
}

.leave-btn-secondary:hover {
	background: var(--border-to);
}

@keyframes slideIn {
	from {
		opacity: 0;
		transform: scale(0.9) translateY(-20px);
	}
	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

/* Dark mode styles for leave confirmation panel */
.dark .leave-confirmation-content {
	background: var(--dark-ash);
	border-color: var(--dark-border);
}

.dark .leave-confirmation-header h3,
.dark .leave-confirmation-body p {
	color: var(--light);
}

.dark .leave-btn-secondary {
	background: var(--dark-light);
	color: var(--light);
	border-color: var(--dark-border);
}

.dark .leave-btn-secondary:hover {
	background: var(--dark-border);
}
